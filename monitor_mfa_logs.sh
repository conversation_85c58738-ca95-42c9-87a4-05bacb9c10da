#!/bin/bash

# Monitor MFA Action Logs in Real-time
# Usage: ./monitor_mfa_logs.sh

AUTH0_DOMAIN="leon-test.au.auth0.com"
CLIENT_ID="0o1g4csayfhT7K5gBLNPj1voSJZsdbyW"
CLIENT_SECRET="****************************************************************"

echo "🔍 Monitoring MFA Action Logs for $AUTH0_DOMAIN"
echo "Press Ctrl+C to stop monitoring"
echo "=================================="

# Function to get access token
get_access_token() {
    curl -s -X POST "https://$AUTH0_DOMAIN/oauth/token" \
        -H "Content-Type: application/json" \
        -d "{
            \"client_id\": \"$CLIENT_ID\",
            \"client_secret\": \"$CLIENT_SECRET\",
            \"audience\": \"https://$AUTH0_DOMAIN/api/v2/\",
            \"grant_type\": \"client_credentials\"
        }" | jq -r '.access_token'
}

# Function to fetch recent logs
fetch_logs() {
    local access_token=$1
    local from_time=$(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%S.000Z)
    
    curl -s -G "https://$AUTH0_DOMAIN/api/v2/logs" \
        -H "Authorization: Bearer $access_token" \
        -H "Content-Type: application/json" \
        --data-urlencode "sort=date:-1" \
        --data-urlencode "per_page=10" \
        --data-urlencode "from=$from_time" \
        --data-urlencode "q=type:(sapi OR fapi OR s OR f)" | jq -r '
        .[] | select(.description | contains("MFA") or contains("Granular") or contains("Action")) |
        "[\(.date)] \(.type) - \(.user_name // "N/A") - \(.client_name // "N/A") - \(.description)"
    '
}

# Main monitoring loop
last_check=""
while true; do
    access_token=$(get_access_token)
    
    if [ "$access_token" != "null" ] && [ -n "$access_token" ]; then
        current_logs=$(fetch_logs "$access_token")
        
        if [ "$current_logs" != "$last_check" ] && [ -n "$current_logs" ]; then
            echo "📋 New MFA-related logs:"
            echo "$current_logs"
            echo "---"
            last_check="$current_logs"
        fi
    else
        echo "❌ Failed to get access token"
    fi
    
    sleep 10
done
