#!/usr/bin/env node

// Debug script to fetch MFA action logs from Auth0
// Usage: node debug_mfa_logs.js

const axios = require('axios');

const AUTH0_DOMAIN = 'leon-test.au.auth0.com';
const CLIENT_ID = '0o1g4csayfhT7K5gBLNPj1voSJZsdbyW';
const CLIENT_SECRET = '****************************************************************';

async function getAccessToken() {
  try {
    const response = await axios.post(`https://${AUTH0_DOMAIN}/oauth/token`, {
      client_id: CLIENT_ID,
      client_secret: CLIENT_SECRET,
      audience: `https://${AUTH0_DOMAIN}/api/v2/`,
      grant_type: 'client_credentials'
    });
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function fetchLogs() {
  try {
    const accessToken = await getAccessToken();
    
    // Fetch logs from the last hour
    const from = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const response = await axios.get(`https://${AUTH0_DOMAIN}/api/v2/logs`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      params: {
        sort: 'date:-1',
        per_page: 50,
        from: from,
        q: 'type:(sapi OR fapi) AND description:*MFA*'
      }
    });

    console.log('=== Recent MFA Action Logs ===');
    console.log(`Found ${response.data.length} log entries\n`);

    response.data.forEach((log, index) => {
      console.log(`--- Log Entry ${index + 1} ---`);
      console.log(`Date: ${log.date}`);
      console.log(`Type: ${log.type}`);
      console.log(`User: ${log.user_name || 'N/A'}`);
      console.log(`Client: ${log.client_name || 'N/A'}`);
      console.log(`IP: ${log.ip || 'N/A'}`);
      
      if (log.details && log.details.response && log.details.response.body) {
        console.log('Response Body:', log.details.response.body);
      }
      
      if (log.description) {
        console.log(`Description: ${log.description}`);
      }
      
      console.log('---\n');
    });

  } catch (error) {
    console.error('Error fetching logs:', error.response?.data || error.message);
  }
}

// Run the script
fetchLogs();
