<!DOCTYPE html>
<html>
<head>
    <title>Test MFA Action - Auth0</title>
    <script src="https://cdn.auth0.com/js/auth0/9.19.0/auth0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test MFA Action</h1>
        <p>This will trigger a login flow to test your MFA Action and generate logs.</p>
        
        <button onclick="testLogin()">🚀 Test Login Flow</button>
        <button onclick="clearLogs()">🗑️ Clear Logs</button>
        
        <div id="logs"></div>
    </div>

    <script>
        // Auth0 Configuration
        const auth0Config = {
            domain: 'leon-test.au.auth0.com',
            clientID: 'LxmTROS8b5mu3Myxyc7SX18EpoNZpxPd',
            redirectUri: window.location.origin + window.location.pathname,
            responseType: 'code',
            scope: 'openid profile email'
        };

        const webAuth = new auth0.WebAuth(auth0Config);

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function testLogin() {
            log('🔄 Starting login flow to trigger MFA Action...');
            log('📋 Check Auth0 Dashboard Logs for Action console output');
            log('🔗 Dashboard: https://leon-test.au.auth0.com/dashboard/us/leon-test/logs');
            
            // Trigger Auth0 login
            webAuth.authorize({
                connection: 'oldbklg-QIVDB', // Your database connection
                prompt: 'login' // Force login to trigger action
            });
        }

        // Handle Auth0 callback
        function handleAuthentication() {
            webAuth.parseHash((err, authResult) => {
                if (authResult && authResult.accessToken && authResult.idToken) {
                    log('✅ Login successful! Check Auth0 logs for MFA Action output.', 'success');
                    log('📊 User Info: ' + JSON.stringify(authResult.idTokenPayload, null, 2));
                    
                    // Store tokens
                    localStorage.setItem('access_token', authResult.accessToken);
                    localStorage.setItem('id_token', authResult.idToken);
                    
                } else if (err) {
                    log('❌ Login error: ' + err.error + ' - ' + err.errorDescription, 'error');
                    console.error(err);
                }
            });
        }

        // Check for Auth0 callback on page load
        window.addEventListener('load', function() {
            if (window.location.hash) {
                handleAuthentication();
            }
            
            log('🎯 MFA Action Test Page Ready');
            log('💡 Click "Test Login Flow" to trigger your MFA Action');
            log('📝 Then check Auth0 Dashboard → Monitoring → Logs for console output');
        });
    </script>
</body>
</html>
