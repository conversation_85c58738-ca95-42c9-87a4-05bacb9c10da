resource "auth0_branding" "main" {
  colors {
    primary = "#0059d6"
    page_background = "#000000"
  }
}

resource "auth0_prompt" "identifier_first" {
  universal_login_experience     = "new"
  identifier_first               = true
  webauthn_platform_first_factor = false
}

resource "auth0_branding_theme" "monarch_theme" {
  borders {
    button_border_radius = 3
    button_border_weight = 1
    buttons_style = "rounded"
    input_border_radius = 3
    input_border_weight = 1
    inputs_style = "rounded"
    show_widget_shadow = true
    widget_border_weight = 0
    widget_corner_radius = 5
  }

  colors {
    body_text = "#000000"
    error = "#d03c2f"
    header = "#000000"
    icons = "#0059d6"
    input_background = "#ffffff"
    input_border = "#c9cace"
    input_filled_text = "#000000"
    input_labels_placeholders = "#65676e"
    links_focused_components = "#0059d6"
    primary_button = "#0059d6"
    primary_button_label = "#ffffff"
    secondary_button_border = "#c9cace"
    secondary_button_label = "#1e212a"
    success = "#13a688"
    widget_background = "#ffffff"
    widget_border = "#c9cace"
  }

  fonts {
    body_text {
      bold = false
      size = 87.5
    }
    buttons_text {
      bold = false
      size = 100.0
    }
    input_labels {
      bold = false
      size = 100.0
    }
    links {
      bold = true
      size = 87.5
    }
    subtitle {
      bold = false
      size = 87.5
    }
    title {
      bold = false
      size = 150.0
    }
  }

  page_background {
    background_color = "#000000"
    background_image_url = ""
    page_layout = "center"
  }

  widget {
    header_text_alignment = "center"
    logo_height = 52.0
    logo_position = "center"
    logo_url = ""
    social_buttons_layout = "bottom"
  }
}

resource "auth0_pages" "login_page" {
  login {
    enabled = true
    html = file("${path.module}/login_page.html")
  }
}