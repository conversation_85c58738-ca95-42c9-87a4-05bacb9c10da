exports.onExecutePostLogin = async (event, api) => {
  const { user, client, request } = event;

  console.log('=== MFA Action Debug Start ===');
  console.log('User ID:', user.user_id);
  console.log('User Email:', user.email);
  console.log('Client ID:', client.client_id);
  console.log('Client Name:', client.name);
  console.log('Grant Type:', event.authorization?.grantType);
  console.log('Request IP:', request.ip);
  console.log('Request User Agent:', request.userAgent);

  // Configuration from Action secrets
  const MFA_CLIENT_IDS = event.secrets.MFA_CLIENT_IDS || '';
  const MFA_USER_EMAILS = event.secrets.MFA_USER_EMAILS || '';

  console.log('MFA_CLIENT_IDS secret:', MFA_CLIENT_IDS);
  console.log('MFA_USER_EMAILS secret:', MFA_USER_EMAILS);

  // Parse comma-separated lists
  const mfaClientIds = MFA_CLIENT_IDS ? MFA_CLIENT_IDS.split(',').map(id => id.trim()) : [];
  const mfaUserEmails = MFA_USER_EMAILS ? MFA_USER_EMAILS.split(',').map(email => email.trim().toLowerCase()) : [];

  console.log('Parsed MFA Client IDs:', mfaClientIds);
  console.log('Parsed MFA User Emails:', mfaUserEmails);

  // Helper function to check if MFA should be required
  function shouldRequireMFA() {
    console.log('--- Checking MFA Requirements ---');

    // If both arrays are empty, require MFA for all
    if (mfaClientIds.length === 0 && mfaUserEmails.length === 0) {
      console.log('Both arrays empty - MFA required for ALL users and clients');
      return true;
    }

    // Check if current client requires MFA
    const clientRequiresMFA = mfaClientIds.length === 0 || mfaClientIds.includes(client.client_id);
    console.log('Client requires MFA:', clientRequiresMFA, '(empty list =', mfaClientIds.length === 0, ', includes client =', mfaClientIds.includes(client.client_id), ')');

    // Check if current user requires MFA
    const userRequiresMFA = mfaUserEmails.length === 0 || mfaUserEmails.includes(user.email.toLowerCase());
    console.log('User requires MFA:', userRequiresMFA, '(empty list =', mfaUserEmails.length === 0, ', includes user =', mfaUserEmails.includes(user.email.toLowerCase()), ')');

    // Require MFA if both client and user conditions are met
    const result = clientRequiresMFA && userRequiresMFA;
    console.log('Final MFA decision:', result);
    return result;
  }

  // Skip MFA for machine-to-machine flows
  if (event.authorization && event.authorization.grantType === 'client_credentials') {
    console.log('Skipping MFA - Machine-to-machine flow detected');
    return;
  }

  // Skip if user doesn't have email
  if (!user.email) {
    console.log('Skipping MFA - User has no email');
    return;
  }

  // Check if MFA should be required
  if (shouldRequireMFA()) {
    console.log('--- MFA Required - Checking Session ---');
    console.log('Authentication methods:', event.authentication?.methods?.map(m => m.name) || 'none');

    // Check if user has already completed MFA in this session
    const mfaAlreadyCompleted = event.authentication && event.authentication.methods.find(method =>
      method.name === 'mfa'
    );

    if (!mfaAlreadyCompleted) {
      console.log('MFA not completed in session - ENABLING MFA');
      // Require MFA (OTP only, no passkey support)
      api.multifactor.enable('any', { allowRememberBrowser: false });

      // Add metadata for monitoring
      api.user.setAppMetadata('mfa_required_by', 'granular_policy');
      api.user.setAppMetadata('mfa_required_at', new Date().toISOString());
    } else {
      console.log('MFA already completed in this session - SKIPPING');
    }
  } else {
    console.log('MFA NOT required for this user/client combination');
  }

  // Add user metadata for audit purposes
  api.user.setAppMetadata('last_login_client', client.client_id);
  api.user.setAppMetadata('last_login_at', new Date().toISOString());

  console.log('=== MFA Action Debug End ===');
};
