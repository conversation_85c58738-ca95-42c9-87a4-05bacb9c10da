resource "auth0_action" "mfa_granular_control" {
  name    = "Granular MFA Control - ${var.env}"
  runtime = "node22"
  deploy  = true
  code    = file("${path.module}/scripts/mfa_action.js")

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }

  secrets {
    name  = "MFA_CLIENT_IDS"
    value = join(",", var.mfa_client_ids)
  }

  secrets {
    name  = "MFA_USER_EMAILS"
    value = join(",", var.mfa_user_emails)
  }

  dependencies {
    name    = "lodash"
    version = "4.17.21"
  }
}

resource "auth0_trigger_action" "mfa_granular_control_binding" {
  trigger = "post-login"
  action_id = auth0_action.mfa_granular_control.id
  display_name = "Granular MFA Control - ${var.env}"
}
