locals {
  mfa_remember_browser_hours = 1  # 1 hour
}

resource "auth0_guardian" "default" {
  policy        = var.mfa_policy
  email         = false
  otp           = true
  recovery_code = true
  mfa_remember_browser_days = local.mfa_remember_browser_hours / 24  # Convert hours to days

  webauthn_platform {
    enabled                = false
    override_relying_party = false
  }

  webauthn_roaming {
    enabled                = false
    override_relying_party = false
    user_verification      = "preferred"
  }
}
